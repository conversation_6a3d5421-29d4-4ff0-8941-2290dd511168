"""
增强的Repository层测试 - 修复版本
================================

专注于测试Repository层实际存在的功能
使用真实数据库操作，确保测试的有效性和价值
"""

from datetime import datetime, timedelta

import pytest
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.order_repository import OrderRepository
from backend.models.order import OrderStatus
from tests.test_utils import TestDataFactory


class TestOrderRepositoryEnhanced:
    """增强的订单Repository测试 - 修复版本"""

    @pytest.fixture
    async def order_repo(self, db_session: AsyncSession):
        """创建OrderRepository实例"""
        return OrderRepository(db_session)

    @pytest.fixture
    async def test_user(self, db_session: AsyncSession):
        """创建测试用户"""
        user = TestDataFactory.create_user_data()
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.mark.asyncio
    async def test_complex_order_queries(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试复杂的订单查询逻辑"""
        # 创建多个不同状态和时间的订单 - 只使用实际存在的状态
        orders_data = [
            {"status": OrderStatus.CREATED, "days_ago": 1},
            {"status": OrderStatus.CANCELLED, "days_ago": 3},
        ]

        created_orders = []
        for data in orders_data:
            order = TestDataFactory.create_order_data(
                {
                    "user_id": test_user.id,
                    "order_status": data["status"].value,
                    "created_at": datetime.now() - timedelta(days=data["days_ago"]),
                }
            )
            created_order = await order_repo.create(order)
            created_orders.append(created_order)

        # 测试按状态查询 - 使用实际存在的方法和状态
        created_orders_result = await order_repo.get_orders_by_status(
            OrderStatus.CREATED, user_id=test_user.id
        )
        assert len(created_orders_result) >= 1
        assert all(
            order.order_status == OrderStatus.CREATED.value
            for order in created_orders_result
        )

        # 测试最近订单查询 - 使用实际存在的方法
        recent_orders = await order_repo.get_recent_orders(
            hours=24 * 7,
            user_id=test_user.id,  # 7天内的订单
        )
        assert len(recent_orders) >= 2  # 应该包含创建的订单

    @pytest.mark.asyncio
    async def test_order_statistics_queries(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试订单统计查询功能"""
        # 创建测试数据 - 只使用实际存在的状态
        statuses = [OrderStatus.CREATED, OrderStatus.CANCELLED]
        for status in statuses:
            for _ in range(2):  # 每种状态创建2个订单
                order = TestDataFactory.create_order_data(
                    {
                        "user_id": test_user.id,
                        "order_status": status.value,
                    }
                )
                await order_repo.create(order)

        # 测试基本的订单查询功能
        created_orders = await order_repo.get_orders_by_status(
            OrderStatus.CREATED, user_id=test_user.id
        )
        assert len(created_orders) == 2

        cancelled_orders = await order_repo.get_orders_by_status(
            OrderStatus.CANCELLED, user_id=test_user.id
        )
        assert len(cancelled_orders) == 2

    @pytest.mark.asyncio
    async def test_order_search_functionality(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试订单搜索功能"""
        # 创建测试订单
        order_data = {
            "user_id": test_user.id,
            "order_status": OrderStatus.CREATED.value,
        }
        order = TestDataFactory.create_order_data(order_data)
        created_order = await order_repo.create(order)

        # 测试按订单号查询
        found_order = await order_repo.get_by_order_no(
            created_order.order_no, user_id=test_user.id
        )
        assert found_order is not None
        assert found_order.id == created_order.id

        # 测试查询不存在的订单号
        not_found = await order_repo.get_by_order_no(
            "NONEXISTENT", user_id=test_user.id
        )
        assert not_found is None

    @pytest.mark.asyncio
    async def test_order_batch_operations(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试批量操作功能"""
        # 创建多个订单
        order_ids = []
        for _ in range(5):
            order = TestDataFactory.create_order_data(
                {
                    "user_id": test_user.id,
                    "order_status": OrderStatus.CREATED.value,
                }
            )
            created_order = await order_repo.create(order)
            order_ids.append(created_order.id)

        # 测试基本的批量查询功能
        for order_id in order_ids:
            order = await order_repo.get_by_id(order_id)
            assert order is not None
            assert order.order_status == OrderStatus.CREATED.value

    @pytest.mark.asyncio
    async def test_order_deletion(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试订单删除功能"""
        # 创建订单
        order = TestDataFactory.create_order_data({"user_id": test_user.id})
        created_order = await order_repo.create(order)

        # 测试删除订单
        await order_repo.delete(created_order)

        # 验证删除结果
        deleted_order = await order_repo.get_by_id(created_order.id)
        assert deleted_order is None

    @pytest.mark.asyncio
    async def test_order_pagination_edge_cases(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试分页查询的边界情况"""
        # 创建大量订单
        total_orders = 25
        for i in range(total_orders):
            order = TestDataFactory.create_order_data(
                {
                    "user_id": test_user.id,
                    "order_no": f"VN20250711TEST{str(i).zfill(3)}",
                }
            )
            await order_repo.create(order)

        # 测试第一页
        page1 = await order_repo.get_by_user_id(test_user.id, limit=10, offset=0)
        assert len(page1) == 10

        # 测试中间页
        page2 = await order_repo.get_by_user_id(test_user.id, limit=10, offset=10)
        assert len(page2) == 10

        # 测试最后一页
        page3 = await order_repo.get_by_user_id(test_user.id, limit=10, offset=20)
        assert len(page3) == 5  # 剩余5个

        # 测试超出范围的页
        page4 = await order_repo.get_by_user_id(test_user.id, limit=10, offset=30)
        assert len(page4) == 0

    @pytest.mark.asyncio
    async def test_order_constraint_violations(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试数据库约束违反的处理"""
        # 创建订单
        order1 = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_no": "VN20250711UNIQUE001",
            }
        )
        await order_repo.create(order1)

        # 尝试创建重复订单号的订单
        order2 = TestDataFactory.create_order_data(
            {
                "user_id": test_user.id,
                "order_no": "VN20250711UNIQUE001",  # 重复的订单号
            }
        )

        with pytest.raises(IntegrityError):
            await order_repo.create(order2)

    @pytest.mark.asyncio
    async def test_order_relationship_queries(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试关联查询功能"""
        # 创建订单
        order = TestDataFactory.create_order_data({"user_id": test_user.id})
        created_order = await order_repo.create(order)

        # 测试带用户信息的查询
        order_with_user = await order_repo.get_with_user(created_order.id)
        assert order_with_user is not None
        assert order_with_user.user is not None
        assert order_with_user.user.id == test_user.id

        # 测试带申请记录的查询
        order_with_applications = await order_repo.get_with_applications(
            created_order.id
        )
        assert order_with_applications is not None

    @pytest.mark.asyncio
    async def test_order_concurrent_updates(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试并发更新处理"""
        import asyncio

        # 创建订单
        order = TestDataFactory.create_order_data({"user_id": test_user.id})
        created_order = await order_repo.create(order)

        # 并发更新测试
        async def update_order_status(status: OrderStatus):
            return await order_repo.update_status(created_order.id, status)

        # 同时执行多个状态更新
        tasks = [
            update_order_status(OrderStatus.PAID),
            update_order_status(OrderStatus.PROCESSING),
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 至少有一个更新应该成功
        successful_updates = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_updates) >= 1

    @pytest.mark.asyncio
    async def test_order_audit_trail(
        self, order_repo: OrderRepository, test_user, db_session: AsyncSession
    ):
        """测试订单审计跟踪功能"""
        # 创建订单
        order = TestDataFactory.create_order_data({"user_id": test_user.id})
        created_order = await order_repo.create(order)

        # 记录状态变更
        await order_repo.log_status_change(
            created_order.id, OrderStatus.CREATED, OrderStatus.PAID, "Payment received"
        )

        # 获取审计记录
        audit_logs = await order_repo.get_audit_trail(created_order.id)
        assert len(audit_logs) >= 1
        assert audit_logs[0]["from_status"] == OrderStatus.CREATED.value
        assert audit_logs[0]["to_status"] == OrderStatus.PAID.value


class TestRepositoryErrorHandlingEnhanced:
    """增强的Repository错误处理测试"""

    @pytest.mark.asyncio
    async def test_database_connection_errors(self, db_session: AsyncSession):
        """测试数据库连接错误处理"""
        # 这里可以测试数据库连接失败的情况
        # 需要根据实际的错误处理逻辑来实现
        pass

    @pytest.mark.asyncio
    async def test_transaction_timeout_handling(self, db_session: AsyncSession):
        """测试事务超时处理"""
        # 测试长时间运行的事务的超时处理
        pass

    @pytest.mark.asyncio
    async def test_deadlock_detection_and_retry(self, db_session: AsyncSession):
        """测试死锁检测和重试机制"""
        # 测试数据库死锁的检测和自动重试
        pass
